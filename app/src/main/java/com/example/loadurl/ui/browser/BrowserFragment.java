package com.example.loadurl.ui.browser;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.loadurl.R;
import com.example.loadurl.databinding.FragmentHomeBinding;
import com.example.loadurl.data.preferences.AppPreferences;

public class BrowserFragment extends Fragment {

    private FragmentHomeBinding binding;
    private BrowserViewModel browserViewModel;
    
    private WebView webView;
    private EditText etUrl;
    private Button btnGo, btnBack, btnForward, btnRefresh, btnHome;
    private ProgressBar progressBar;
    private AppPreferences appPreferences;

    private static final String DEFAULT_URL = "https://www.baidu.com";

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        browserViewModel = new ViewModelProvider(this).get(BrowserViewModel.class);

        binding = FragmentHomeBinding.inflate(inflater, container, false);
        View root = binding.getRoot();

        appPreferences = new AppPreferences(requireContext());

        initViews(root);
        setupWebView();
        setupClickListeners();

        // 处理从历史记录传来的URL参数
        Bundle args = getArguments();
        if (args != null && args.containsKey("url")) {
            String url = args.getString("url");
            loadUrl(url);
        } else {
            // 加载默认页面
            loadUrl(DEFAULT_URL);
        }

        return root;
    }

    private void initViews(View root) {
        webView = root.findViewById(R.id.webview);
        etUrl = root.findViewById(R.id.et_url);
        btnGo = root.findViewById(R.id.btn_go);
        btnBack = root.findViewById(R.id.btn_back);
        btnForward = root.findViewById(R.id.btn_forward);
        btnRefresh = root.findViewById(R.id.btn_refresh);
        btnHome = root.findViewById(R.id.btn_home);
        progressBar = root.findViewById(R.id.progress_bar);
    }

    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);

        // 根据设置配置缓存
        updateCacheSettings(webSettings);
        
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBar.setVisibility(View.VISIBLE);
                etUrl.setText(url);
                updateNavigationButtons();
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBar.setVisibility(View.GONE);
                updateNavigationButtons();
                
                // 保存到历史记录
                String title = view.getTitle();
                if (title == null || title.isEmpty()) {
                    title = url;
                }
                browserViewModel.saveToHistory(url, title);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                progressBar.setVisibility(View.GONE);

                String errorMessage;
                if (appPreferences.isOfflineMode()) {
                    errorMessage = "断网模式下无法加载此页面，请检查是否已缓存该页面";
                } else {
                    errorMessage = getString(R.string.load_error) + ": " + description;
                }
                Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show();
            }
        });

        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                progressBar.setProgress(newProgress);
            }
        });
    }

    private void setupClickListeners() {
        btnGo.setOnClickListener(v -> {
            String url = etUrl.getText().toString().trim();
            if (!url.isEmpty()) {
                loadUrl(url);
            }
        });

        btnBack.setOnClickListener(v -> {
            if (webView.canGoBack()) {
                webView.goBack();
            }
        });

        btnForward.setOnClickListener(v -> {
            if (webView.canGoForward()) {
                webView.goForward();
            }
        });

        btnRefresh.setOnClickListener(v -> webView.reload());

        btnHome.setOnClickListener(v -> loadUrl(DEFAULT_URL));

        // 地址栏回车键监听
        etUrl.setOnKeyListener((v, keyCode, event) -> {
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN) {
                String url = etUrl.getText().toString().trim();
                if (!url.isEmpty()) {
                    loadUrl(url);
                }
                return true;
            }
            return false;
        });
    }

    private void loadUrl(String url) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        webView.loadUrl(url);
        etUrl.setText(url);
    }

    private void updateNavigationButtons() {
        btnBack.setEnabled(webView.canGoBack());
        btnForward.setEnabled(webView.canGoForward());
    }

    private void updateCacheSettings(WebSettings webSettings) {
        if (appPreferences.isOfflineMode()) {
            // 断网模式：只使用缓存
            webSettings.setCacheMode(WebSettings.LOAD_CACHE_ONLY);
        } else if (appPreferences.isCacheEnabled()) {
            // 启用缓存：优先使用缓存，缓存过期时从网络加载
            webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        } else {
            // 禁用缓存：总是从网络加载
            webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 每次回到页面时更新缓存设置
        if (webView != null) {
            updateCacheSettings(webView.getSettings());
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
