package com.example.loadurl.ui.browser;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;

import com.example.loadurl.data.database.AppDatabase;
import com.example.loadurl.data.database.HistoryDao;
import com.example.loadurl.data.model.HistoryItem;

import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class BrowserViewModel extends AndroidViewModel {
    
    private final HistoryDao historyDao;
    private final ExecutorService executor;

    public BrowserViewModel(@NonNull Application application) {
        super(application);
        AppDatabase database = AppDatabase.getDatabase(application);
        historyDao = database.historyDao();
        executor = Executors.newFixedThreadPool(2);
    }

    public void saveToHistory(String url, String title) {
        executor.execute(() -> {
            HistoryItem item = new HistoryItem();
            item.url = url;
            item.title = title;
            item.visitTime = new Date();
            historyDao.insert(item);
        });
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
    }
}
