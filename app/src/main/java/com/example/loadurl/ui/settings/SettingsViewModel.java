package com.example.loadurl.ui.settings;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;

import com.example.loadurl.data.preferences.AppPreferences;

public class SettingsViewModel extends AndroidViewModel {
    
    private final AppPreferences appPreferences;

    public SettingsViewModel(@NonNull Application application) {
        super(application);
        appPreferences = new AppPreferences(application);
    }

    public boolean isCacheEnabled() {
        return appPreferences.isCacheEnabled();
    }

    public void setCacheEnabled(boolean enabled) {
        appPreferences.setCacheEnabled(enabled);
    }

    public boolean isOfflineMode() {
        return appPreferences.isOfflineMode();
    }

    public void setOfflineMode(boolean offline) {
        appPreferences.setOfflineMode(offline);
    }
}
