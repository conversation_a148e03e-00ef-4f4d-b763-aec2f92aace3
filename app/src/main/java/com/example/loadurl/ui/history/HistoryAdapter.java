package com.example.loadurl.ui.history;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.loadurl.R;
import com.example.loadurl.data.model.HistoryItem;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

public class HistoryAdapter extends RecyclerView.Adapter<HistoryAdapter.HistoryViewHolder> {
    
    private List<HistoryItem> historyList = new ArrayList<>();
    private Set<Integer> selectedItems = new HashSet<>();
    private OnHistoryItemClickListener listener;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());

    public interface OnHistoryItemClickListener {
        void onItemClick(HistoryItem item);
        void onDeleteClick(HistoryItem item);
        void onSelectionChanged(int selectedCount);
    }

    public HistoryAdapter(OnHistoryItemClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public HistoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_history, parent, false);
        return new HistoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull HistoryViewHolder holder, int position) {
        HistoryItem item = historyList.get(position);
        
        holder.tvTitle.setText(item.title);
        holder.tvUrl.setText(item.url);
        holder.tvTime.setText(dateFormat.format(item.visitTime));
        
        holder.cbSelect.setChecked(selectedItems.contains(item.id));
        
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onItemClick(item);
            }
        });
        
        holder.btnDelete.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeleteClick(item);
            }
        });
        
        holder.cbSelect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                selectedItems.add(item.id);
            } else {
                selectedItems.remove(item.id);
            }
            if (listener != null) {
                listener.onSelectionChanged(selectedItems.size());
            }
        });
    }

    @Override
    public int getItemCount() {
        return historyList.size();
    }

    public void setHistoryList(List<HistoryItem> historyList) {
        this.historyList = historyList;
        selectedItems.clear();
        notifyDataSetChanged();
        if (listener != null) {
            listener.onSelectionChanged(0);
        }
    }

    public void selectAll() {
        selectedItems.clear();
        for (HistoryItem item : historyList) {
            selectedItems.add(item.id);
        }
        notifyDataSetChanged();
        if (listener != null) {
            listener.onSelectionChanged(selectedItems.size());
        }
    }

    public void clearSelection() {
        selectedItems.clear();
        notifyDataSetChanged();
        if (listener != null) {
            listener.onSelectionChanged(0);
        }
    }

    public List<Integer> getSelectedIds() {
        return new ArrayList<>(selectedItems);
    }

    public boolean hasSelection() {
        return !selectedItems.isEmpty();
    }

    static class HistoryViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle, tvUrl, tvTime;
        CheckBox cbSelect;
        Button btnDelete;

        public HistoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvUrl = itemView.findViewById(R.id.tv_url);
            tvTime = itemView.findViewById(R.id.tv_time);
            cbSelect = itemView.findViewById(R.id.cb_select);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }
    }
}
