package com.example.loadurl.ui.history;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.example.loadurl.data.database.AppDatabase;
import com.example.loadurl.data.database.HistoryDao;
import com.example.loadurl.data.model.HistoryItem;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class HistoryViewModel extends AndroidViewModel {
    
    private final HistoryDao historyDao;
    private final LiveData<List<HistoryItem>> allHistory;
    private final ExecutorService executor;

    public HistoryViewModel(@NonNull Application application) {
        super(application);
        AppDatabase database = AppDatabase.getDatabase(application);
        historyDao = database.historyDao();
        allHistory = historyDao.getAllHistory();
        executor = Executors.newFixedThreadPool(2);
    }

    public LiveData<List<HistoryItem>> getAllHistory() {
        return allHistory;
    }

    public void deleteHistoryItem(HistoryItem item) {
        executor.execute(() -> historyDao.delete(item));
    }

    public void deleteSelectedHistory(List<Integer> ids) {
        executor.execute(() -> historyDao.deleteByIds(ids));
    }

    public void clearAllHistory() {
        executor.execute(() -> historyDao.deleteAll());
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
    }
}
