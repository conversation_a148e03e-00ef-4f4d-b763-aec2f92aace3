package com.example.loadurl.ui.settings;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.loadurl.R;
import com.example.loadurl.databinding.FragmentSlideshowBinding;

public class SettingsFragment extends Fragment {

    private FragmentSlideshowBinding binding;
    private SettingsViewModel settingsViewModel;
    
    private SwitchCompat switchCache, switchOffline;
    private Button btnClearCache;

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        settingsViewModel = new ViewModelProvider(this).get(SettingsViewModel.class);

        binding = FragmentSlideshowBinding.inflate(inflater, container, false);
        View root = binding.getRoot();

        initViews(root);
        setupClickListeners();
        loadSettings();

        return root;
    }

    private void initViews(View root) {
        switchCache = root.findViewById(R.id.switch_cache);
        switchOffline = root.findViewById(R.id.switch_offline);
        btnClearCache = root.findViewById(R.id.btn_clear_cache);
    }

    private void setupClickListeners() {
        switchCache.setOnCheckedChangeListener((buttonView, isChecked) -> {
            settingsViewModel.setCacheEnabled(isChecked);
            if (isChecked) {
                Toast.makeText(getContext(), "网页缓存已启用", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(getContext(), "网页缓存已禁用", Toast.LENGTH_SHORT).show();
            }
        });

        switchOffline.setOnCheckedChangeListener((buttonView, isChecked) -> {
            settingsViewModel.setOfflineMode(isChecked);
            if (isChecked) {
                Toast.makeText(getContext(), getString(R.string.offline_mode_enabled), Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(getContext(), getString(R.string.offline_mode_disabled), Toast.LENGTH_SHORT).show();
            }
        });

        btnClearCache.setOnClickListener(v -> {
            clearWebViewCache();
            Toast.makeText(getContext(), getString(R.string.cache_cleared), Toast.LENGTH_SHORT).show();
        });
    }

    private void loadSettings() {
        switchCache.setChecked(settingsViewModel.isCacheEnabled());
        switchOffline.setChecked(settingsViewModel.isOfflineMode());
    }

    private void clearWebViewCache() {
        if (getContext() != null) {
            // 清理WebView缓存
            WebView webView = new WebView(getContext());
            webView.clearCache(true);
            webView.clearHistory();
            webView.clearFormData();
            webView.destroy();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
