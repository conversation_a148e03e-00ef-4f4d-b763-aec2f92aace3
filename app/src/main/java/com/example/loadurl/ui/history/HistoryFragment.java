package com.example.loadurl.ui.history;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.loadurl.R;
import com.example.loadurl.data.model.HistoryItem;
import com.example.loadurl.databinding.FragmentGalleryBinding;

public class HistoryFragment extends Fragment implements HistoryAdapter.OnHistoryItemClickListener {

    private FragmentGalleryBinding binding;
    private HistoryViewModel historyViewModel;
    private HistoryAdapter adapter;
    
    private RecyclerView rvHistory;
    private TextView tvEmpty;
    private Button btnSelectAll, btnDeleteSelected, btnClearAll;
    private boolean isSelectAllMode = false;

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        historyViewModel = new ViewModelProvider(this).get(HistoryViewModel.class);

        binding = FragmentGalleryBinding.inflate(inflater, container, false);
        View root = binding.getRoot();

        initViews(root);
        setupRecyclerView();
        setupClickListeners();
        observeData();

        return root;
    }

    private void initViews(View root) {
        rvHistory = root.findViewById(R.id.rv_history);
        tvEmpty = root.findViewById(R.id.tv_empty);
        btnSelectAll = root.findViewById(R.id.btn_select_all);
        btnDeleteSelected = root.findViewById(R.id.btn_delete_selected);
        btnClearAll = root.findViewById(R.id.btn_clear_all);
    }

    private void setupRecyclerView() {
        adapter = new HistoryAdapter(this);
        rvHistory.setLayoutManager(new LinearLayoutManager(getContext()));
        rvHistory.setAdapter(adapter);
    }

    private void setupClickListeners() {
        btnSelectAll.setOnClickListener(v -> {
            if (isSelectAllMode) {
                adapter.clearSelection();
                btnSelectAll.setText("全选");
                isSelectAllMode = false;
            } else {
                adapter.selectAll();
                btnSelectAll.setText("取消全选");
                isSelectAllMode = true;
            }
        });

        btnDeleteSelected.setOnClickListener(v -> {
            if (adapter.hasSelection()) {
                showDeleteSelectedDialog();
            }
        });

        btnClearAll.setOnClickListener(v -> showClearAllDialog());
    }

    private void observeData() {
        historyViewModel.getAllHistory().observe(getViewLifecycleOwner(), historyItems -> {
            if (historyItems == null || historyItems.isEmpty()) {
                rvHistory.setVisibility(View.GONE);
                tvEmpty.setVisibility(View.VISIBLE);
            } else {
                rvHistory.setVisibility(View.VISIBLE);
                tvEmpty.setVisibility(View.GONE);
                adapter.setHistoryList(historyItems);
            }
        });
    }

    @Override
    public void onItemClick(HistoryItem item) {
        // 导航到浏览器并加载该URL
        Bundle bundle = new Bundle();
        bundle.putString("url", item.url);
        Navigation.findNavController(requireView()).navigate(R.id.nav_browser, bundle);
    }

    @Override
    public void onDeleteClick(HistoryItem item) {
        historyViewModel.deleteHistoryItem(item);
        Toast.makeText(getContext(), "已删除", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onSelectionChanged(int selectedCount) {
        btnDeleteSelected.setEnabled(selectedCount > 0);
        if (selectedCount == 0) {
            btnSelectAll.setText("全选");
            isSelectAllMode = false;
        }
    }

    private void showDeleteSelectedDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle("确认删除")
                .setMessage(getString(R.string.confirm_delete_selected))
                .setPositiveButton(getString(R.string.ok), (dialog, which) -> {
                    historyViewModel.deleteSelectedHistory(adapter.getSelectedIds());
                    Toast.makeText(getContext(), "已删除选中项", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    private void showClearAllDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle("确认清空")
                .setMessage(getString(R.string.confirm_delete_all))
                .setPositiveButton(getString(R.string.ok), (dialog, which) -> {
                    historyViewModel.clearAllHistory();
                    Toast.makeText(getContext(), "历史记录已清空", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
