package com.example.loadurl.data.preferences;

import android.content.Context;
import android.content.SharedPreferences;

public class AppPreferences {
    private static final String PREF_NAME = "browser_preferences";
    private static final String KEY_CACHE_ENABLED = "cache_enabled";
    private static final String KEY_OFFLINE_MODE = "offline_mode";
    
    private SharedPreferences preferences;
    
    public AppPreferences(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }
    
    public boolean isCacheEnabled() {
        return preferences.getBoolean(KEY_CACHE_ENABLED, true);
    }
    
    public void setCacheEnabled(boolean enabled) {
        preferences.edit().putBoolean(KEY_CACHE_ENABLED, enabled).apply();
    }
    
    public boolean isOfflineMode() {
        return preferences.getBoolean(KEY_OFFLINE_MODE, false);
    }
    
    public void setOfflineMode(boolean offline) {
        preferences.edit().putBoolean(KEY_OFFLINE_MODE, offline).apply();
    }
}
