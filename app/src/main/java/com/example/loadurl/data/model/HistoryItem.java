package com.example.loadurl.data.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

import java.util.Date;

@Entity(tableName = "history")
public class HistoryItem {
    @PrimaryKey(autoGenerate = true)
    public int id;
    
    public String url;
    public String title;
    public Date visitTime;

    public HistoryItem() {}

    public HistoryItem(String url, String title, Date visitTime) {
        this.url = url;
        this.title = title;
        this.visitTime = visitTime;
    }
}
