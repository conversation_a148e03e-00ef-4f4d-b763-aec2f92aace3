package com.example.loadurl.data.database;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.example.loadurl.data.model.HistoryItem;

import java.util.List;

@Dao
public interface HistoryDao {
    
    @Query("SELECT * FROM history ORDER BY visitTime DESC")
    LiveData<List<HistoryItem>> getAllHistory();
    
    @Query("SELECT * FROM history ORDER BY visitTime DESC")
    List<HistoryItem> getAllHistorySync();
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(HistoryItem historyItem);
    
    @Delete
    void delete(HistoryItem historyItem);
    
    @Query("DELETE FROM history WHERE id IN (:ids)")
    void deleteByIds(List<Integer> ids);
    
    @Query("DELETE FROM history")
    void deleteAll();
    
    @Query("SELECT COUNT(*) FROM history")
    int getHistoryCount();
}
