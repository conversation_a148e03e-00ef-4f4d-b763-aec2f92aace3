<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- 选择框 -->
    <CheckBox
        android:id="@+id/cb_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical" />

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="12dp"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- URL -->
        <TextView
            android:id="@+id/tv_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 访问时间 -->
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

    <!-- 删除按钮 -->
    <Button
        android:id="@+id/btn_delete"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:text="×"
        android:textSize="20sp"
        android:layout_gravity="center_vertical"
        style="@style/Widget.Material3.Button.IconButton" />

</LinearLayout>
