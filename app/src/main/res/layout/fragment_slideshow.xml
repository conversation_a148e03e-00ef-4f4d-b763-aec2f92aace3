<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.settings.SettingsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 缓存设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/cache_settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_cache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/enable_cache"
            android:textSize="16sp"
            android:checked="true"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_clear_cache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/clear_cache"
            android:layout_marginBottom="32dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- 网络设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/network_settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_offline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/simulate_offline"
            android:textSize="16sp"
            android:checked="false"
            android:layout_marginBottom="16dp" />

        <!-- 说明文字 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启用断网模拟后，将只能访问已缓存的网页内容"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</ScrollView>