<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.browser.BrowserFragment">

    <!-- 地址栏和控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@color/design_default_color_primary_variant">

        <!-- 后退按钮 -->
        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="◀"
            android:textSize="16sp"
            android:enabled="false"
            android:textColor="@android:color/white"
            android:background="?attr/selectableItemBackgroundBorderless" />

        <!-- 前进按钮 -->
        <Button
            android:id="@+id/btn_forward"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="▶"
            android:textSize="16sp"
            android:enabled="false"
            android:textColor="@android:color/white"
            android:background="?attr/selectableItemBackgroundBorderless" />

        <!-- 刷新按钮 -->
        <Button
            android:id="@+id/btn_refresh"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="⟳"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:background="?attr/selectableItemBackgroundBorderless" />

        <!-- 首页按钮 -->
        <Button
            android:id="@+id/btn_home"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="🏠"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:background="?attr/selectableItemBackgroundBorderless" />

        <!-- 地址栏 -->
        <EditText
            android:id="@+id/et_url"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_gravity="center_vertical"
            android:hint="@string/url_hint"
            android:inputType="textUri"
            android:singleLine="true"
            android:imeOptions="actionGo"
            android:background="@android:drawable/edit_text"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:textSize="14sp" />

        <!-- 访问按钮 -->
        <Button
            android:id="@+id/btn_go"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:text="@string/go_button"
            android:textSize="14sp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        style="@android:style/Widget.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:visibility="gone" />

    <!-- WebView -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>