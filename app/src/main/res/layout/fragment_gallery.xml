<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.history.HistoryFragment">

    <!-- 工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/design_default_color_primary_variant">

        <Button
            android:id="@+id/btn_select_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="全选"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_delete_selected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/delete_selected_history"
            android:enabled="false"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btn_clear_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/delete_all_history"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <!-- 历史记录列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_history"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp" />

    <!-- 空状态提示 -->
    <TextView
        android:id="@+id/tv_empty"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="@string/history_empty"
        android:textSize="18sp"
        android:textColor="@android:color/darker_gray"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>