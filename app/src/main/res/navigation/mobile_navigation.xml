<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_browser">

    <fragment
        android:id="@+id/nav_browser"
        android:name="com.example.loadurl.ui.browser.BrowserFragment"
        android:label="@string/menu_browser"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/nav_history"
        android:name="com.example.loadurl.ui.history.HistoryFragment"
        android:label="@string/menu_history"
        tools:layout="@layout/fragment_gallery" />

    <fragment
        android:id="@+id/nav_settings"
        android:name="com.example.loadurl.ui.settings.SettingsFragment"
        android:label="@string/menu_settings"
        tools:layout="@layout/fragment_slideshow" />
</navigation>