plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.example.loadurl'
    compileSdk 36

    defaultConfig {
        applicationId "com.example.loadurl"
        minSdk 31
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.constraintlayout
    implementation libs.lifecycle.livedata.ktx
    implementation libs.lifecycle.viewmodel.ktx
    implementation libs.navigation.fragment
    implementation libs.navigation.ui

    // Room数据库
    implementation libs.room.runtime
    annotationProcessor libs.room.compiler

    // 设置界面
    implementation libs.preference

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}